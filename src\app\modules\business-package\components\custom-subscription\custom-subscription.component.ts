import { Component, Input } from '@angular/core';
import { CustomDetailsComponent } from "../custom-details/custom-details.component";
import { InputValueComponent } from "@src/app/shared/components/input-value/input-value.component";
import { NewRangeInputComponent } from "@src/app/shared/components/new-range-input/new-range-input.component";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-custom-subscription',
  standalone: true,
  imports: [CustomDetailsComponent, InputValueComponent, NewRangeInputComponent, SharedBtnComponent],
  templateUrl: './custom-subscription.component.html',
  styleUrl: './custom-subscription.component.scss'
})
export class CustomSubscriptionComponent {
  @Input() titleText: string = '';
  constructor(public ref: DynamicDialogRef, public config: DynamicDialogConfig) {}
  ngOnInit() {
    Object.assign(this, this.config?.data ?? {});
  }

  
  payCustom() {
    this.ref?.close({ action: 'pay-custom' });
    console.log('pay-custom');
    
  }
}
