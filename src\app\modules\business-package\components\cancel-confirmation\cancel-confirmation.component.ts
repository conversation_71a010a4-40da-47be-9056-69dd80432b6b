import { Component, Input } from '@angular/core';
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackageDetailsComponent } from "../package-details/package-details.component";
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-cancel-confirmation',
  standalone: true,
  imports: [SharedBtnComponent],
  templateUrl: './cancel-confirmation.component.html',
  styleUrl: './cancel-confirmation.component.scss'
})
export class CancelConfirmationComponent {

  @Input() titleText: string = '';
  @Input() cancelDate: string = '';
  @Input() cancelDateValue: string | null = '';
  @Input() btnText: string = '';
  constructor(public ref: DynamicDialogRef, public config: DynamicDialogConfig) {}
  ngOnInit() {
    Object.assign(this, this.config?.data ?? {});
  }

  cancel() {
    this.ref?.close({ action: 'cancel' });
    console.log('cancel');
    
  }
}
