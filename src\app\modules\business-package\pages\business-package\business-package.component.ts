import { Component, inject } from '@angular/core';
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackagePlanComponent } from "../../components/package-plan/package-plan.component";
import { CustomPackagePlanComponent } from "../../components/custom-package-plan/custom-package-plan.component";
import { ViewSwitchService } from '../../services/view-switch.service';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { CancelConfirmationComponent } from '../../components/cancel-confirmation/cancel-confirmation.component';
import { DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-business-package',
  standalone: true,
  imports: [SharedBtnComponent, PackagePlanComponent, CustomPackagePlanComponent],
  templateUrl: './business-package.component.html',
  styleUrl: './business-package.component.scss'
})
export class BusinessPackageComponent {

  private alerts = inject(AlertHandlerService);
  constructor(public ref: DynamicDialogRef) {}

   goToSubscription() {
    // 1) اقفل الديالوج الحالي (اللي عارض الـ BusinessPackage أو حتى BusinessPackageCycle)
    this.ref?.close();

    // 2) افتح ديالوج التأكيد بعد إغلاق السابق
    setTimeout(() => {
      this.alerts.DynamicDialogOpen(
        CancelConfirmationComponent,
        {
          titleText: 'هل أنت متأكد من رغبتك في تحويل الي حساب فرد؟  ',
          cancelDate: 'عند تحويل نوع الحساب ، سيتم إزالة أي إضافة او اعلانات تم شراؤها مسبقًا',
          btnText: 'تأكيد التحويل ',
        },
        (res) => {
          if (res?.confirmed) {
            // نفّذ التحويل هنا لو حابب
          }
        },
        'alertModal',
        { modal: true, dismissableMask: false }
      );
    }, 0);
  }
}
